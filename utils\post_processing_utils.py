import json
from typing import Any, Dict, List
import re
import logging


def convert_text_to_dict(text):
    cleaned_text = text.replace("```json\n", "").replace("\n```", "")
    try:
        return json.loads(cleaned_text)
    except:
        return eval(cleaned_text)


async def post_process_data_points(
    answers: Dict[str, Any], data_points: List[str]
) -> Dict[str, Any]:
    """
    Post process data points.
    Args:
        answers (dict): The answers.
        data_points (List[str]): The data points.
    Returns:
        dict: The post processed data points.
    """
    updated_answers = {
        data_point: (
            answers[data_point]
            if data_point in answers
            and answers[data_point] is not None
            and "None" != answers[data_point]
            else ""
        )
        for data_point in data_points
    }
    updated_answers["Chunks"] = answers.get("Chunks")
    return updated_answers


def _extract_json_with_regex(response: str) -> Dict[str, Any]:
    """
    Extracts JSON with regex.
    Args:
        response (str): The response from the LLM.
    Returns:
        Dict[str, Any]: The extracted JSON.
    """
    line_re = re.compile(r"\{[^\}]+\}")
    records = line_re.findall(response)
    try:
        if not records:
            return response
        llm_json_response = convert_text_to_dict(records[0])
        return llm_json_response
    except Exception as e:
        logging.exception(f"Not able to extract JSON with regex. Error: {e}")
        return response


def extract_json_from_text(llm_text_response: str) -> Dict[str, Any]:
    """
    Extracts JSON from text.
    Args:
        llm_text_response (str): The response from the LLM.
    Returns:
        Dict[str, Any]: The extracted JSON.
    """
    try:
        llm_json_response = convert_text_to_dict(llm_text_response)
    except Exception as e:
        logging.exception(
            f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
        )
        llm_json_response = _extract_json_with_regex(llm_text_response)

    if isinstance(llm_json_response, dict):
        return llm_json_response
    return None
